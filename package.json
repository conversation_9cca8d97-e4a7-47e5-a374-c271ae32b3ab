{"name": "ngrx-starwars", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~10.1.1", "@angular/cdk": "~10.2.0", "@angular/common": "~10.1.1", "@angular/compiler": "~10.1.1", "@angular/core": "~10.1.1", "@angular/flex-layout": "^10.0.0-beta.32", "@angular/forms": "~10.1.1", "@angular/material": "^10.2.0", "@angular/platform-browser": "~10.1.1", "@angular/platform-browser-dynamic": "~10.1.1", "@angular/router": "~10.1.1", "@ngrx/effects": "^8.3.0", "@ngrx/store": "^8.3.0", "@ngrx/store-devtools": "^8.3.0", "rxjs": "~6.6.3", "tslib": "^2.0.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1001.1", "@angular/cli": "~10.1.1", "@angular/compiler-cli": "~10.1.1", "@angular/language-service": "~10.1.1", "@types/node": "^12.11.1", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.0.2"}}